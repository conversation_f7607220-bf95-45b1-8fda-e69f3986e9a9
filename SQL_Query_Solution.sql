-- SQL Server 查询语句：合并两个表生成Table3
-- 根据您的需求，将Table1和Table2的A列数据合并，并生成序列号

-- 方案1：简单合并并按字母顺序排序（推荐）
SELECT 
    A,
    ROW_NUMBER() OVER (ORDER BY A) AS 序列
FROM (
    SELECT A FROM Table1
    UNION ALL
    SELECT A FROM Table2
) AS CombinedData
ORDER BY A;

-- 方案2：如果需要保持特定的排序（先Table1后Table2）
SELECT 
    A,
    ROW_NUMBER() OVER (ORDER BY SourceOrder, A) AS 序列
FROM (
    SELECT A, 1 AS SourceOrder FROM Table1
    UNION ALL
    SELECT A, 2 AS SourceOrder FROM Table2
) AS CombinedData
ORDER BY SourceOrder, A;

-- 方案3：如果需要按照原始表中的顺序排列
WITH CombinedData AS (
    SELECT A, ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) AS OriginalOrder, 1 AS TableSource FROM Table1
    UNION ALL
    SELECT A, ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) AS OriginalOrder, 2 AS TableSource FROM Table2
)
SELECT 
    A,
    ROW_NUMBER() OVER (ORDER BY TableSource, OriginalOrder) AS 序列
FROM CombinedData
ORDER BY TableSource, OriginalOrder;

-- 方案4：如果您的表有主键或其他排序字段
-- 假设Table1和Table2都有ID字段
/*
SELECT 
    A,
    ROW_NUMBER() OVER (ORDER BY TableSource, ID) AS 序列
FROM (
    SELECT A, ID, 1 AS TableSource FROM Table1
    UNION ALL
    SELECT A, ID, 2 AS TableSource FROM Table2
) AS CombinedData
ORDER BY TableSource, ID;
*/

-- 创建测试数据（可选，用于验证）
/*
-- 创建测试表
CREATE TABLE #Table1 (A VARCHAR(10));
CREATE TABLE #Table2 (A VARCHAR(10));

-- 插入测试数据
INSERT INTO #Table1 VALUES ('8J'), ('9J'), ('12J');
INSERT INTO #Table2 VALUES ('10C'), ('11C'), ('13C');

-- 执行查询
SELECT 
    A,
    ROW_NUMBER() OVER (ORDER BY A) AS 序列
FROM (
    SELECT A FROM #Table1
    UNION ALL
    SELECT A FROM #Table2
) AS CombinedData
ORDER BY A;

-- 清理测试表
DROP TABLE #Table1;
DROP TABLE #Table2;
*/
