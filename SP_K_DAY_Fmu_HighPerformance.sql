ALTER PROCEDURE [dbo].[SP_K_DAY_Fmu_HighPerformance] 
    @sDateBegin varchar(10),
    @sDateEnd varchar(10),
    @FmuBMs varchar(100)
AS
BEGIN
    SET NOCOUNT ON;
    SET ARITHABORT OFF;        -- 提高计算性能
    SET ANSI_WARNINGS OFF;     -- 减少警告检查开销
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;  -- 允许脏读，提高速度
    
    BEGIN TRY
        
        -- 性能优化1：预先检查必要条件，避免无效操作
        IF @sDateBegin IS NULL OR @sDateEnd IS NULL OR @FmuBMs IS NULL OR LEN(@FmuBMs) = 0
        BEGIN
            RETURN  -- 直接返回，不抛错误
        END
        
        -- 性能优化2：快速检查是否有公式需要处理
        DECLARE @HasFormula BIT = 0
        EXEC('IF EXISTS(SELECT 1 FROM GZ_FmuSet WHERE BM0000 IN ('+@FmuBMs+')) SET @HasFormula = 1', @HasFormula OUTPUT)
        
        IF @HasFormula = 0
        BEGIN
            RETURN  -- 没有公式直接返回
        END
        
        -- 性能优化3：使用表变量替代临时表（小数据量时更快）
        DECLARE @FumSet TABLE(
            FMUBM INT NOT NULL,
            Table_Name varchar(255) NULL,
            PRIMARY KEY (FMUBM)  -- 添加主键提高查询速度
        )
        
        EXEC('INSERT INTO @FumSet (FMUBM,Table_Name) SELECT DISTINCT BM0000,PATBM FROM GZ_FmuSet WHERE BM0000 IN ('+@FmuBMs+')')
        
        -- 性能优化4：只在需要时创建#K_Day，并优化数据获取
        IF OBJECT_ID('tempdb..#K_Day') IS NULL
        BEGIN
            -- 使用NOLOCK提高读取速度
            IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'A01')
            BEGIN
                SELECT K.* INTO #K_Day  
                FROM K_Day K WITH(NOLOCK)
                INNER JOIN A01 WITH(NOLOCK) ON K.A0188 = A01.A0188 
                WHERE K.duty_date >= @sDateBegin 
                  AND K.duty_date <= @sDateEnd 
            END
            ELSE
            BEGIN
                SELECT * INTO #K_Day  
                FROM K_Day WITH(NOLOCK)
                WHERE duty_date >= @sDateBegin 
                  AND duty_date <= @sDateEnd 
            END
            
            -- 创建聚集索引提高UPDATE性能
            IF EXISTS(SELECT 1 FROM #K_Day)
            BEGIN
                CREATE CLUSTERED INDEX IX_K_Day_Main ON #K_Day (A0188, duty_date)
            END
            ELSE
            BEGIN
                RETURN  -- 没有数据直接返回
            END
        END
        
        -- 性能优化5：使用CTE一次性获取并处理所有公式
        WITH FormulaCTE AS (
            SELECT 
                ROW_NUMBER() OVER(ORDER BY A.FMUBM, A.FormulaOrder) AS RowNum,
                -- 在CTE中直接进行字符串替换，减少后续UPDATE操作
                REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(
                REPLACE(REPLACE(
                    CAST(A.Sql_Final AS NVARCHAR(MAX)),
                    'K_Day.', '#K_Day.'),
                    'K_Day ', '#K_Day '),
                    'K_Card.', '#K_Card.'),
                    'K_Card ', '#K_Card '),
                    'K_Leave.', '#K_Leave.'),
                    'K_Leave ', '#K_Leave '),
                    'K_Over.', '#K_Over.'),
                    'K_Over ', '#K_Over '),
                    'K_Out.', '#K_Out.'),
                    'K_Out ', '#K_Out '),
                    'K_Travel.', '#K_Travel.'),
                    'K_Travel ', '#K_Travel ')
                AS Sql_Final_Processed
            FROM GZ_FMU A WITH(NOLOCK)
            INNER JOIN @FumSet B ON A.FMUBM = B.FMUBM 
            WHERE A.Takeit = 1
        )
        SELECT 
            RowNum,
            -- 在最终SELECT中替换日期参数
            REPLACE(REPLACE(Sql_Final_Processed, '开始日期', '''' + @sDateBegin + ''''), '结束日期', '''' + @sDateEnd + '''') AS Sql_Final
        INTO #GZ_FMU_Fast
        FROM FormulaCTE
        WHERE LEN(Sql_Final_Processed) > 0
        ORDER BY RowNum
        
        -- 检查是否有有效公式
        DECLARE @FormulaCount INT
        SELECT @FormulaCount = COUNT(*) FROM #GZ_FMU_Fast
        
        IF @FormulaCount = 0
        BEGIN
            RETURN  -- 没有有效公式直接返回
        END
        
        -- 性能优化6：使用更高效的字符串拼接方法
        DECLARE @Sql_Final1 NVARCHAR(MAX) = N''
        DECLARE @BatchSize INT = 50  -- 分批处理，避免SQL过长
        DECLARE @CurrentBatch INT = 1
        DECLARE @TotalBatches INT = (@FormulaCount + @BatchSize - 1) / @BatchSize
        
        WHILE @CurrentBatch <= @TotalBatches
        BEGIN
            DECLARE @BatchStart INT = (@CurrentBatch - 1) * @BatchSize + 1
            DECLARE @BatchEnd INT = @CurrentBatch * @BatchSize
            
            -- 使用STRING_AGG或FOR XML PATH快速拼接（根据SQL Server版本）
            IF @@VERSION LIKE '%201[7-9]%' OR @@VERSION LIKE '%202%'
            BEGIN
                -- SQL Server 2017+ 使用STRING_AGG
                SELECT @Sql_Final1 = STRING_AGG(N'UPDATE #K_Day ' + Sql_Final + N';', N'') WITHIN GROUP (ORDER BY RowNum)
                FROM #GZ_FMU_Fast 
                WHERE RowNum BETWEEN @BatchStart AND @BatchEnd
            END
            ELSE
            BEGIN
                -- 老版本使用FOR XML PATH（比WHILE循环快）
                SELECT @Sql_Final1 = STUFF((
                    SELECT N' UPDATE #K_Day ' + Sql_Final + N';'
                    FROM #GZ_FMU_Fast 
                    WHERE RowNum BETWEEN @BatchStart AND @BatchEnd
                    ORDER BY RowNum
                    FOR XML PATH(''), TYPE
                ).value('.', 'NVARCHAR(MAX)'), 1, 0, N'')
            END
            
            -- 执行当前批次
            IF LEN(@Sql_Final1) > 0
            BEGIN
                EXEC sp_executesql @Sql_Final1
                SET @Sql_Final1 = N''
            END
            
            SET @CurrentBatch = @CurrentBatch + 1
        END
        
    END TRY
    BEGIN CATCH
        -- 简化错误处理，减少开销
        DECLARE @ErrorMessage NVARCHAR(1000) = LEFT(ERROR_MESSAGE(), 1000)
        RAISERROR(@ErrorMessage, 16, 1)
    END CATCH
    
    -- 清理（只清理我们创建的表）
    IF OBJECT_ID('tempdb..#GZ_FMU_Fast') IS NOT NULL 
        DROP TABLE #GZ_FMU_Fast
    
END
